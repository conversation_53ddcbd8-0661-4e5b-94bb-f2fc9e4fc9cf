{"version": "2.0", "last_updated": "2025-09-01T18:07:10.758778", "global_settings": {"auto_generate_mappings": true, "enable_smart_suggestions": true, "save_edit_history": true, "preserve_chinese_headers": true}, "table_mappings": {"salary_data_2025_07_active_employees": {"工号": "employee_id", "姓名": "employee_name", "部门名称": "department", "人员类别": "employee_type", "人员类别代码": "employee_type_code", "2025年岗位工资": "position_salary_2025", "2025年薪级工资": "grade_salary_2025", "津贴": "allowance", "结余津贴": "balance_allowance", "应发工资": "total_salary"}, "active_employees": {"工号": "employee_id", "姓名": "employee_name", "部门名称": "department", "人员类别": "employee_type", "人员类别代码": "employee_type_code"}, "change_data_2025_12_全部在职人员工资表": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-08-28T23:42:25.201434", "table_type": "change_table"}}, "change_data_2025_12_退休人员工资表": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-08-28T23:43:14.366414", "table_type": "change_table"}}, "change_data_2025_12_A岗职工": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-08-28T23:43:16.491189", "table_type": "change_table"}}, "change_data_2025_12_离休人员工资表": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-08-28T23:44:08.471691", "table_type": "change_table"}}, "salary_data_2025_05": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "sequence_number": "序号", "row_number": "行号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "performance_bonus_2025": "float", "total_salary": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "supplement": "float", "advance": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "sequence_number": "integer", "row_number": "integer"}, "hidden_fields": ["id", "created_at", "updated_at", "sequence_number", "row_number", "sequence"], "display_order": [], "metadata": {"created_at": "2025-09-01T08:57:36.647117", "table_type": "unknown"}}, "mapping_config_20250901_162809": {"metadata": {"created_at": "2025-09-01T16:28:09.240442", "last_modified": "2025-09-01T16:28:09.240442", "auto_generated": false, "user_modified": true}, "field_mappings": {"test_field": "test_column"}, "edit_history": []}, "mapping_config_20250901_164641": {"metadata": {"created_at": "2025-09-01T16:46:41.009915", "last_modified": "2025-09-01T16:46:41.009915", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_164654": {"metadata": {"created_at": "2025-09-01T16:46:54.767915", "last_modified": "2025-09-01T16:46:54.767915", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_164758": {"metadata": {"created_at": "2025-09-01T16:47:58.890786", "last_modified": "2025-09-01T16:47:58.890786", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_164803": {"metadata": {"created_at": "2025-09-01T16:48:03.672118", "last_modified": "2025-09-01T16:48:03.672118", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_164808": {"metadata": {"created_at": "2025-09-01T16:48:08.346447", "last_modified": "2025-09-01T16:48:08.346447", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_164812": {"metadata": {"created_at": "2025-09-01T16:48:12.007600", "last_modified": "2025-09-01T16:48:12.007600", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_164819": {"metadata": {"created_at": "2025-09-01T16:48:19.417605", "last_modified": "2025-09-01T16:48:19.417605", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_164821": {"metadata": {"created_at": "2025-09-01T16:48:21.534979", "last_modified": "2025-09-01T16:48:21.534979", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_164840": {"metadata": {"created_at": "2025-09-01T16:48:40.502527", "last_modified": "2025-09-01T16:48:40.502527", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_164939": {"metadata": {"created_at": "2025-09-01T16:49:39.031973", "last_modified": "2025-09-01T16:49:39.031973", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_164940": {"metadata": {"created_at": "2025-09-01T16:49:40.350656", "last_modified": "2025-09-01T16:49:40.350656", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165009": {"metadata": {"created_at": "2025-09-01T16:50:09.983348", "last_modified": "2025-09-01T16:50:09.983348", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165020": {"metadata": {"created_at": "2025-09-01T16:50:20.560384", "last_modified": "2025-09-01T16:50:20.560384", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165028": {"metadata": {"created_at": "2025-09-01T16:50:28.170192", "last_modified": "2025-09-01T16:50:28.170192", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165031": {"metadata": {"created_at": "2025-09-01T16:50:31.706379", "last_modified": "2025-09-01T16:50:31.706379", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165047": {"metadata": {"created_at": "2025-09-01T16:50:47.762678", "last_modified": "2025-09-01T16:50:47.762678", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165050": {"metadata": {"created_at": "2025-09-01T16:50:50.386294", "last_modified": "2025-09-01T16:50:50.386294", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165053": {"metadata": {"created_at": "2025-09-01T16:50:53.095666", "last_modified": "2025-09-01T16:50:53.095666", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165054": {"metadata": {"created_at": "2025-09-01T16:50:54.383186", "last_modified": "2025-09-01T16:50:54.383186", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165055": {"metadata": {"created_at": "2025-09-01T16:50:55.880213", "last_modified": "2025-09-01T16:50:55.880213", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165057": {"metadata": {"created_at": "2025-09-01T16:50:57.143233", "last_modified": "2025-09-01T16:50:57.143233", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165110": {"metadata": {"created_at": "2025-09-01T16:51:10.182365", "last_modified": "2025-09-01T16:51:10.182365", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165111": {"metadata": {"created_at": "2025-09-01T16:51:11.502226", "last_modified": "2025-09-01T16:51:11.502226", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165112": {"metadata": {"created_at": "2025-09-01T16:51:12.566556", "last_modified": "2025-09-01T16:51:12.566556", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165113": {"metadata": {"created_at": "2025-09-01T16:51:13.575711", "last_modified": "2025-09-01T16:51:13.575711", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165130": {"metadata": {"created_at": "2025-09-01T16:51:30.593739", "last_modified": "2025-09-01T16:51:30.593739", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165131": {"metadata": {"created_at": "2025-09-01T16:51:31.895581", "last_modified": "2025-09-01T16:51:31.895581", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165132": {"metadata": {"created_at": "2025-09-01T16:51:32.799770", "last_modified": "2025-09-01T16:51:32.799770", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165134": {"metadata": {"created_at": "2025-09-01T16:51:34.175754", "last_modified": "2025-09-01T16:51:34.175754", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165138": {"metadata": {"created_at": "2025-09-01T16:51:38.039542", "last_modified": "2025-09-01T16:51:38.039542", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165139": {"metadata": {"created_at": "2025-09-01T16:51:39.048261", "last_modified": "2025-09-01T16:51:39.048261", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165140": {"metadata": {"created_at": "2025-09-01T16:51:40.328143", "last_modified": "2025-09-01T16:51:40.328143", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165143": {"metadata": {"created_at": "2025-09-01T16:51:43.368355", "last_modified": "2025-09-01T16:51:43.368355", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165146": {"metadata": {"created_at": "2025-09-01T16:51:46.471272", "last_modified": "2025-09-01T16:51:46.471272", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165147": {"metadata": {"created_at": "2025-09-01T16:51:47.847023", "last_modified": "2025-09-01T16:51:47.847023", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165149": {"metadata": {"created_at": "2025-09-01T16:51:49.294848", "last_modified": "2025-09-01T16:51:49.294848", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165156": {"metadata": {"created_at": "2025-09-01T16:51:56.206950", "last_modified": "2025-09-01T16:51:56.206950", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165202": {"metadata": {"created_at": "2025-09-01T16:52:02.615408", "last_modified": "2025-09-01T16:52:02.615408", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165206": {"metadata": {"created_at": "2025-09-01T16:52:06.263186", "last_modified": "2025-09-01T16:52:06.263186", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165208": {"metadata": {"created_at": "2025-09-01T16:52:08.048127", "last_modified": "2025-09-01T16:52:08.048127", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_165241": {"metadata": {"created_at": "2025-09-01T16:52:41.058260", "last_modified": "2025-09-01T16:52:41.058260", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165246": {"metadata": {"created_at": "2025-09-01T16:52:46.707203", "last_modified": "2025-09-01T16:52:46.707203", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165248": {"metadata": {"created_at": "2025-09-01T16:52:48.456039", "last_modified": "2025-09-01T16:52:48.456039", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165249": {"metadata": {"created_at": "2025-09-01T16:52:49.240959", "last_modified": "2025-09-01T16:52:49.960537", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165250": {"metadata": {"created_at": "2025-09-01T16:52:50.864321", "last_modified": "2025-09-01T16:52:50.864321", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165256": {"metadata": {"created_at": "2025-09-01T16:52:56.711317", "last_modified": "2025-09-01T16:52:56.711317", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165257": {"metadata": {"created_at": "2025-09-01T16:52:57.248035", "last_modified": "2025-09-01T16:52:57.881077", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165258": {"metadata": {"created_at": "2025-09-01T16:52:58.839858", "last_modified": "2025-09-01T16:52:58.839858", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165307": {"metadata": {"created_at": "2025-09-01T16:53:07.424022", "last_modified": "2025-09-01T16:53:07.424022", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165308": {"metadata": {"created_at": "2025-09-01T16:53:08.303384", "last_modified": "2025-09-01T16:53:08.783806", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165309": {"metadata": {"created_at": "2025-09-01T16:53:09.568909", "last_modified": "2025-09-01T16:53:09.568909", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165327": {"metadata": {"created_at": "2025-09-01T16:53:27.959901", "last_modified": "2025-09-01T16:53:27.959901", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165328": {"metadata": {"created_at": "2025-09-01T16:53:28.576976", "last_modified": "2025-09-01T16:53:28.576976", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165329": {"metadata": {"created_at": "2025-09-01T16:53:29.199711", "last_modified": "2025-09-01T16:53:29.199711", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_165330": {"metadata": {"created_at": "2025-09-01T16:53:30.631798", "last_modified": "2025-09-01T16:53:30.631798", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_171907": {"metadata": {"created_at": "2025-09-01T17:19:07.543260", "last_modified": "2025-09-01T17:19:07.971695", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金"}, "edit_history": []}, "mapping_config_20250901_171908": {"metadata": {"created_at": "2025-09-01T17:19:08.013843", "last_modified": "2025-09-01T17:19:08.926701", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_171917": {"metadata": {"created_at": "2025-09-01T17:19:17.058447", "last_modified": "2025-09-01T17:19:17.991935", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_171918": {"metadata": {"created_at": "2025-09-01T17:19:18.033526", "last_modified": "2025-09-01T17:19:18.492954", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_171921": {"metadata": {"created_at": "2025-09-01T17:19:21.394369", "last_modified": "2025-09-01T17:19:22.001044", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金"}, "edit_history": []}, "mapping_config_20250901_171922": {"metadata": {"created_at": "2025-09-01T17:19:22.074698", "last_modified": "2025-09-01T17:19:23.002935", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_171923": {"metadata": {"created_at": "2025-09-01T17:19:23.062941", "last_modified": "2025-09-01T17:19:23.088239", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_171924": {"metadata": {"created_at": "2025-09-01T17:19:24.956364", "last_modified": "2025-09-01T17:19:25.009834", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_171925": {"metadata": {"created_at": "2025-09-01T17:19:25.147359", "last_modified": "2025-09-01T17:19:25.974232", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_171926": {"metadata": {"created_at": "2025-09-01T17:19:26.027923", "last_modified": "2025-09-01T17:19:26.691277", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_171929": {"metadata": {"created_at": "2025-09-01T17:19:29.638197", "last_modified": "2025-09-01T17:19:30.001669", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资"}, "edit_history": []}, "mapping_config_20250901_171930": {"metadata": {"created_at": "2025-09-01T17:19:30.128509", "last_modified": "2025-09-01T17:19:30.977189", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_171931": {"metadata": {"created_at": "2025-09-01T17:19:31.064333", "last_modified": "2025-09-01T17:19:31.519891", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资", "绩效奖金": "绩效奖金", "部门": "部门"}, "edit_history": []}, "mapping_config_20250901_172314": {"metadata": {"created_at": "2025-09-01T17:23:14.477149", "last_modified": "2025-09-01T17:23:15.001678", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴"}, "edit_history": []}, "mapping_config_20250901_172315": {"metadata": {"created_at": "2025-09-01T17:23:15.014038", "last_modified": "2025-09-01T17:23:15.881666", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172324": {"metadata": {"created_at": "2025-09-01T17:23:24.235240", "last_modified": "2025-09-01T17:23:24.235240", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172327": {"metadata": {"created_at": "2025-09-01T17:23:27.810239", "last_modified": "2025-09-01T17:23:27.810239", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172334": {"metadata": {"created_at": "2025-09-01T17:23:34.138402", "last_modified": "2025-09-01T17:23:34.138402", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172340": {"metadata": {"created_at": "2025-09-01T17:23:40.826471", "last_modified": "2025-09-01T17:23:40.826471", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172341": {"metadata": {"created_at": "2025-09-01T17:23:41.635632", "last_modified": "2025-09-01T17:23:41.635632", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172342": {"metadata": {"created_at": "2025-09-01T17:23:42.742154", "last_modified": "2025-09-01T17:23:42.742154", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172344": {"metadata": {"created_at": "2025-09-01T17:23:44.817035", "last_modified": "2025-09-01T17:23:44.817035", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172349": {"metadata": {"created_at": "2025-09-01T17:23:49.072656", "last_modified": "2025-09-01T17:23:49.072656", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172355": {"metadata": {"created_at": "2025-09-01T17:23:55.353266", "last_modified": "2025-09-01T17:23:55.994825", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发"}, "edit_history": []}, "mapping_config_20250901_172356": {"metadata": {"created_at": "2025-09-01T17:23:56.014832", "last_modified": "2025-09-01T17:23:56.608690", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172400": {"metadata": {"created_at": "2025-09-01T17:24:00.624604", "last_modified": "2025-09-01T17:24:00.990418", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172401": {"metadata": {"created_at": "2025-09-01T17:24:01.006228", "last_modified": "2025-09-01T17:24:01.994818", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支"}, "edit_history": []}, "mapping_config_20250901_172402": {"metadata": {"created_at": "2025-09-01T17:24:02.007988", "last_modified": "2025-09-01T17:24:02.310351", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172409": {"metadata": {"created_at": "2025-09-01T17:24:09.609271", "last_modified": "2025-09-01T17:24:09.989349", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发"}, "edit_history": []}, "mapping_config_20250901_172410": {"metadata": {"created_at": "2025-09-01T17:24:10.014182", "last_modified": "2025-09-01T17:24:10.939969", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172413": {"metadata": {"created_at": "2025-09-01T17:24:13.106015", "last_modified": "2025-09-01T17:24:13.990177", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172414": {"metadata": {"created_at": "2025-09-01T17:24:14.003416", "last_modified": "2025-09-01T17:24:14.994058", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支"}, "edit_history": []}, "mapping_config_20250901_172415": {"metadata": {"created_at": "2025-09-01T17:24:15.013397", "last_modified": "2025-09-01T17:24:15.335938", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172429": {"metadata": {"created_at": "2025-09-01T17:24:29.342115", "last_modified": "2025-09-01T17:24:30.000893", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整"}, "edit_history": []}, "mapping_config_20250901_172430": {"metadata": {"created_at": "2025-09-01T17:24:30.014571", "last_modified": "2025-09-01T17:24:30.958748", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整"}, "edit_history": []}, "mapping_config_20250901_172431": {"metadata": {"created_at": "2025-09-01T17:24:31.025952", "last_modified": "2025-09-01T17:24:31.763374", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172543": {"metadata": {"created_at": "2025-09-01T17:25:43.488849", "last_modified": "2025-09-01T17:25:43.999888", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发"}, "edit_history": []}, "mapping_config_20250901_172544": {"metadata": {"created_at": "2025-09-01T17:25:44.011556", "last_modified": "2025-09-01T17:25:44.751922", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172552": {"metadata": {"created_at": "2025-09-01T17:25:52.083701", "last_modified": "2025-09-01T17:25:52.083701", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172555": {"metadata": {"created_at": "2025-09-01T17:25:55.155633", "last_modified": "2025-09-01T17:25:55.155633", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172558": {"metadata": {"created_at": "2025-09-01T17:25:58.153591", "last_modified": "2025-09-01T17:25:58.153591", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172600": {"metadata": {"created_at": "2025-09-01T17:26:00.603910", "last_modified": "2025-09-01T17:26:00.603910", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172602": {"metadata": {"created_at": "2025-09-01T17:26:02.153935", "last_modified": "2025-09-01T17:26:02.153935", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172603": {"metadata": {"created_at": "2025-09-01T17:26:03.106329", "last_modified": "2025-09-01T17:26:03.106329", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172606": {"metadata": {"created_at": "2025-09-01T17:26:06.673165", "last_modified": "2025-09-01T17:26:06.673165", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172608": {"metadata": {"created_at": "2025-09-01T17:26:08.065455", "last_modified": "2025-09-01T17:26:08.065455", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172611": {"metadata": {"created_at": "2025-09-01T17:26:11.959739", "last_modified": "2025-09-01T17:26:11.959739", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172612": {"metadata": {"created_at": "2025-09-01T17:26:12.083366", "last_modified": "2025-09-01T17:26:12.982418", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172613": {"metadata": {"created_at": "2025-09-01T17:26:13.002507", "last_modified": "2025-09-01T17:26:13.625833", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_172616": {"metadata": {"created_at": "2025-09-01T17:26:16.329022", "last_modified": "2025-09-01T17:26:16.993975", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发"}, "edit_history": []}, "mapping_config_20250901_172617": {"metadata": {"created_at": "2025-09-01T17:26:17.008576", "last_modified": "2025-09-01T17:26:17.728499", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172619": {"metadata": {"created_at": "2025-09-01T17:26:19.889617", "last_modified": "2025-09-01T17:26:19.889617", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172620": {"metadata": {"created_at": "2025-09-01T17:26:20.015642", "last_modified": "2025-09-01T17:26:20.990742", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172621": {"metadata": {"created_at": "2025-09-01T17:26:21.004756", "last_modified": "2025-09-01T17:26:21.993073", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款"}, "edit_history": []}, "mapping_config_20250901_172622": {"metadata": {"created_at": "2025-09-01T17:26:22.013460", "last_modified": "2025-09-01T17:26:22.106753", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172623": {"metadata": {"created_at": "2025-09-01T17:26:23.529343", "last_modified": "2025-09-01T17:26:24.000041", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整"}, "edit_history": []}, "mapping_config_20250901_172624": {"metadata": {"created_at": "2025-09-01T17:26:24.020780", "last_modified": "2025-09-01T17:26:24.999635", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172625": {"metadata": {"created_at": "2025-09-01T17:26:25.014111", "last_modified": "2025-09-01T17:26:25.014111", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172627": {"metadata": {"created_at": "2025-09-01T17:26:27.153122", "last_modified": "2025-09-01T17:26:28.000365", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_172628": {"metadata": {"created_at": "2025-09-01T17:26:28.020127", "last_modified": "2025-09-01T17:26:28.906641", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173216": {"metadata": {"created_at": "2025-09-01T17:32:16.155180", "last_modified": "2025-09-01T17:32:16.979468", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴"}, "edit_history": []}, "mapping_config_20250901_173217": {"metadata": {"created_at": "2025-09-01T17:32:17.002999", "last_modified": "2025-09-01T17:32:17.998972", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173218": {"metadata": {"created_at": "2025-09-01T17:32:18.016286", "last_modified": "2025-09-01T17:32:18.016286", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173239": {"metadata": {"created_at": "2025-09-01T17:32:39.421658", "last_modified": "2025-09-01T17:32:39.421658", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173241": {"metadata": {"created_at": "2025-09-01T17:32:41.094032", "last_modified": "2025-09-01T17:32:41.094032", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173243": {"metadata": {"created_at": "2025-09-01T17:32:43.821604", "last_modified": "2025-09-01T17:32:43.821604", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173245": {"metadata": {"created_at": "2025-09-01T17:32:45.870039", "last_modified": "2025-09-01T17:32:45.870039", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173248": {"metadata": {"created_at": "2025-09-01T17:32:48.900290", "last_modified": "2025-09-01T17:32:48.900290", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173249": {"metadata": {"created_at": "2025-09-01T17:32:49.674060", "last_modified": "2025-09-01T17:32:49.674060", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173251": {"metadata": {"created_at": "2025-09-01T17:32:51.515920", "last_modified": "2025-09-01T17:32:51.515920", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173253": {"metadata": {"created_at": "2025-09-01T17:32:53.099629", "last_modified": "2025-09-01T17:32:53.099629", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173256": {"metadata": {"created_at": "2025-09-01T17:32:56.436275", "last_modified": "2025-09-01T17:32:56.985002", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发"}, "edit_history": []}, "mapping_config_20250901_173257": {"metadata": {"created_at": "2025-09-01T17:32:57.009527", "last_modified": "2025-09-01T17:32:57.989842", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支"}, "edit_history": []}, "mapping_config_20250901_173258": {"metadata": {"created_at": "2025-09-01T17:32:58.003608", "last_modified": "2025-09-01T17:32:58.079535", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_173300": {"metadata": {"created_at": "2025-09-01T17:33:00.388095", "last_modified": "2025-09-01T17:33:01.000286", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_173301": {"metadata": {"created_at": "2025-09-01T17:33:01.019790", "last_modified": "2025-09-01T17:33:01.994712", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发"}, "edit_history": []}, "mapping_config_20250901_173302": {"metadata": {"created_at": "2025-09-01T17:33:02.009450", "last_modified": "2025-09-01T17:33:02.551471", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173306": {"metadata": {"created_at": "2025-09-01T17:33:06.740760", "last_modified": "2025-09-01T17:33:06.986519", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173307": {"metadata": {"created_at": "2025-09-01T17:33:07.003335", "last_modified": "2025-09-01T17:33:07.989757", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173308": {"metadata": {"created_at": "2025-09-01T17:33:08.003456", "last_modified": "2025-09-01T17:33:08.995808", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金"}, "edit_history": []}, "mapping_config_20250901_173309": {"metadata": {"created_at": "2025-09-01T17:33:09.015451", "last_modified": "2025-09-01T17:33:09.994065", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173310": {"metadata": {"created_at": "2025-09-01T17:33:10.011620", "last_modified": "2025-09-01T17:33:10.998982", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_173311": {"metadata": {"created_at": "2025-09-01T17:33:11.016780", "last_modified": "2025-09-01T17:33:11.982296", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款"}, "edit_history": []}, "mapping_config_20250901_173312": {"metadata": {"created_at": "2025-09-01T17:33:12.011992", "last_modified": "2025-09-01T17:33:12.135190", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_173314": {"metadata": {"created_at": "2025-09-01T17:33:14.084415", "last_modified": "2025-09-01T17:33:15.001711", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整"}, "edit_history": []}, "mapping_config_20250901_173315": {"metadata": {"created_at": "2025-09-01T17:33:15.022249", "last_modified": "2025-09-01T17:33:15.764776", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "edit_history": []}, "mapping_config_20250901_174436": {"metadata": {"created_at": "2025-09-01T17:44:36.949968", "last_modified": "2025-09-01T17:44:36.949968", "auto_generated": false, "user_modified": true}, "field_mappings": {"姓名": "姓名", "工号": "工号", "基本工资": "基本工资"}, "edit_history": []}, "mapping_config_20250901_175147": {"metadata": {"created_at": "2025-09-01T17:51:47.410519", "last_modified": "2025-09-01T17:51:47.410931", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_175149": {"metadata": {"created_at": "2025-09-01T17:51:49.534549", "last_modified": "2025-09-01T17:51:49.534549", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_175153": {"metadata": {"created_at": "2025-09-01T17:51:53.068476", "last_modified": "2025-09-01T17:51:53.068476", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_175154": {"metadata": {"created_at": "2025-09-01T17:51:54.979582", "last_modified": "2025-09-01T17:51:54.979582", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_175155": {"metadata": {"created_at": "2025-09-01T17:51:55.547749", "last_modified": "2025-09-01T17:51:55.547749", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_175156": {"metadata": {"created_at": "2025-09-01T17:51:56.708108", "last_modified": "2025-09-01T17:51:56.708108", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_175201": {"metadata": {"created_at": "2025-09-01T17:52:01.170810", "last_modified": "2025-09-01T17:52:01.170810", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_175202": {"metadata": {"created_at": "2025-09-01T17:52:02.889990", "last_modified": "2025-09-01T17:52:02.889990", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "mapping_config_20250901_180638": {"metadata": {"created_at": "2025-09-01T18:06:38.182763", "last_modified": "2025-09-01T18:06:38.182763", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180640": {"metadata": {"created_at": "2025-09-01T18:06:40.351105", "last_modified": "2025-09-01T18:06:40.351105", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180644": {"metadata": {"created_at": "2025-09-01T18:06:44.102784", "last_modified": "2025-09-01T18:06:44.102784", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180645": {"metadata": {"created_at": "2025-09-01T18:06:45.890501", "last_modified": "2025-09-01T18:06:45.890501", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180648": {"metadata": {"created_at": "2025-09-01T18:06:48.691153", "last_modified": "2025-09-01T18:06:48.691153", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180651": {"metadata": {"created_at": "2025-09-01T18:06:51.976408", "last_modified": "2025-09-01T18:06:51.976408", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180653": {"metadata": {"created_at": "2025-09-01T18:06:53.298388", "last_modified": "2025-09-01T18:06:53.298388", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180654": {"metadata": {"created_at": "2025-09-01T18:06:54.568335", "last_modified": "2025-09-01T18:06:54.568335", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180657": {"metadata": {"created_at": "2025-09-01T18:06:57.331828", "last_modified": "2025-09-01T18:06:57.331828", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180658": {"metadata": {"created_at": "2025-09-01T18:06:58.859771", "last_modified": "2025-09-01T18:06:58.859771", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "department", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}, "mapping_config_20250901_180710": {"metadata": {"created_at": "2025-09-01T18:07:10.758778", "last_modified": "2025-09-01T18:07:10.758778", "auto_generated": false, "user_modified": true}, "field_mappings": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年岗位工资": {"target_field": "field_2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年校龄工资": {"target_field": "field_2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年基础性绩效": {"target_field": "field_2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年生活补贴": {"target_field": "field_2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "field_2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "2025公积金": {"target_field": "field_2025公积金", "display_name": "2025公积金", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "general", "data_type": "VARCHAR(100)", "is_required": false}}, "edit_history": []}}, "field_templates": {"离休人员工资表": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "basic_retirement_salary": "基本离休费", "balance_allowance": "结余津贴", "living_allowance": "生活补贴", "housing_allowance": "住房补贴", "property_allowance": "物业补贴", "retirement_allowance": "离休补贴", "nursing_fee": "护理费", "one_time_living_allowance": "增发一次性生活补贴", "supplement": "补发", "total": "合计", "advance": "借支", "remarks": "备注"}, "退休人员工资表": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注"}, "全部在职人员工资表": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险"}, "A岗职工": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "seniority_salary_2025": "2025年校龄工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "living_allowance_2025": "2025年生活补贴", "car_allowance": "车补", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "insurance_deduction": "保险扣款", "pension_insurance": "代扣代存养老保险"}}, "user_preferences": {"default_field_patterns": {}, "recent_edits": [], "favorite_mappings": []}}