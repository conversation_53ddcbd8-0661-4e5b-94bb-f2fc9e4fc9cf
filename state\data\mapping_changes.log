{"timestamp": "2025-08-28T23:42:00.099905", "table_name": "change_data_2025_12_retired_employees", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-08-28T23:42:00.362498", "table_name": "change_data_2025_12_pension_employees", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-08-28T23:42:00.671453", "table_name": "change_data_2025_12_active_employees", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-08-28T23:42:00.977748", "table_name": "change_data_2025_12_a_grade_employees", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:28:09.245070", "table_name": "mapping_config_20250901_162809", "action": "save", "field_count": 1, "fields": ["test_field"]}
{"timestamp": "2025-09-01T16:46:41.013981", "table_name": "mapping_config_20250901_164641", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:46:54.773310", "table_name": "mapping_config_20250901_164654", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:47:58.894348", "table_name": "mapping_config_20250901_164758", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:48:03.675200", "table_name": "mapping_config_20250901_164803", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:48:08.350048", "table_name": "mapping_config_20250901_164808", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:48:12.012532", "table_name": "mapping_config_20250901_164812", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:48:19.422168", "table_name": "mapping_config_20250901_164819", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:48:21.539595", "table_name": "mapping_config_20250901_164821", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:48:40.507132", "table_name": "mapping_config_20250901_164840", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:49:39.036051", "table_name": "mapping_config_20250901_164939", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:49:40.355238", "table_name": "mapping_config_20250901_164940", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:09.988393", "table_name": "mapping_config_20250901_165009", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:20.564431", "table_name": "mapping_config_20250901_165020", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:28.175880", "table_name": "mapping_config_20250901_165028", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:31.711415", "table_name": "mapping_config_20250901_165031", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:47.767777", "table_name": "mapping_config_20250901_165047", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:50.391336", "table_name": "mapping_config_20250901_165050", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:53.100755", "table_name": "mapping_config_20250901_165053", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:54.389216", "table_name": "mapping_config_20250901_165054", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:55.884835", "table_name": "mapping_config_20250901_165055", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:50:57.148866", "table_name": "mapping_config_20250901_165057", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:51:10.189460", "table_name": "mapping_config_20250901_165110", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:51:11.507456", "table_name": "mapping_config_20250901_165111", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:51:12.572550", "table_name": "mapping_config_20250901_165112", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:51:13.581344", "table_name": "mapping_config_20250901_165113", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-01T16:51:30.599339", "table_name": "mapping_config_20250901_165130", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:51:31.901854", "table_name": "mapping_config_20250901_165131", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:51:32.805348", "table_name": "mapping_config_20250901_165132", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:51:34.180826", "table_name": "mapping_config_20250901_165134", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:51:38.045112", "table_name": "mapping_config_20250901_165138", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:51:39.055402", "table_name": "mapping_config_20250901_165139", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:51:40.334413", "table_name": "mapping_config_20250901_165140", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:51:43.373593", "table_name": "mapping_config_20250901_165143", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:51:46.477337", "table_name": "mapping_config_20250901_165146", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:51:47.853589", "table_name": "mapping_config_20250901_165147", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:51:49.301144", "table_name": "mapping_config_20250901_165149", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:51:56.214069", "table_name": "mapping_config_20250901_165156", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:52:02.621490", "table_name": "mapping_config_20250901_165202", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:52:06.272001", "table_name": "mapping_config_20250901_165206", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:52:08.054902", "table_name": "mapping_config_20250901_165208", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-01T16:52:41.065917", "table_name": "mapping_config_20250901_165241", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:52:46.714270", "table_name": "mapping_config_20250901_165246", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:52:48.463196", "table_name": "mapping_config_20250901_165248", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:52:49.248652", "table_name": "mapping_config_20250901_165249", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:52:49.967611", "table_name": "mapping_config_20250901_165249", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:52:50.870660", "table_name": "mapping_config_20250901_165250", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:52:56.718944", "table_name": "mapping_config_20250901_165256", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:52:57.255144", "table_name": "mapping_config_20250901_165257", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:52:57.887706", "table_name": "mapping_config_20250901_165257", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:52:58.846751", "table_name": "mapping_config_20250901_165258", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:53:07.432109", "table_name": "mapping_config_20250901_165307", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:53:08.311172", "table_name": "mapping_config_20250901_165308", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:53:08.791910", "table_name": "mapping_config_20250901_165308", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:53:09.575550", "table_name": "mapping_config_20250901_165309", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:53:27.968033", "table_name": "mapping_config_20250901_165327", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:53:28.584094", "table_name": "mapping_config_20250901_165328", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:53:29.208392", "table_name": "mapping_config_20250901_165329", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:53:30.638358", "table_name": "mapping_config_20250901_165330", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
