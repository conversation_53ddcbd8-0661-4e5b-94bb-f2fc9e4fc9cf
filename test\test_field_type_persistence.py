#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
字段类型持久化测试：验证字段类型修改后的保存和恢复
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.log_config import setup_logger

logger = setup_logger(__name__)


def test_field_type_save_and_restore():
    """测试字段类型的保存和恢复"""
    logger.info("🧪 测试字段类型保存和恢复功能")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.unified_data_import_window import UnifiedMappingConfigWidget
        from unittest.mock import Mock
        
        if not QApplication.instance():
            app = QApplication([])
        
        # 创建组件
        widget = UnifiedMappingConfigWidget()
        
        # 模拟config_sync_manager
        mock_config_sync = Mock()
        mock_config_sync.save_mapping.return_value = True
        mock_config_sync.load_mapping.return_value = None
        widget.config_sync_manager = mock_config_sync
        
        # 加载测试数据
        test_headers = ["姓名", "工号", "基本工资"]
        widget.load_excel_headers(test_headers, "salary_table")
        
        # 模拟用户修改字段类型
        logger.info("📝 模拟用户修改字段类型")
        
        # 获取第一行的字段类型下拉框
        field_type_combo = widget.mapping_table.cellWidget(0, 3)
        if field_type_combo:
            # 修改为"员工编号"类型
            for i in range(field_type_combo.count()):
                if field_type_combo.itemData(i) == "employee_id":
                    field_type_combo.setCurrentIndex(i)
                    logger.info(f"设置字段 '姓名' 的类型为: employee_id")
                    break
        
        # 触发保存
        widget._update_mapping_config()
        widget._save_mapping_config_immediately()
        
        # 检查保存的配置
        saved_config = widget.get_mapping_config()
        logger.info(f"保存的配置: {json.dumps(saved_config, ensure_ascii=False, indent=2)}")
        
        # 验证字段类型是否正确保存
        if "姓名" in saved_config:
            field_config = saved_config["姓名"]
            if field_config.get("field_type") == "employee_id":
                logger.info("✅ 字段类型保存正确")
            else:
                logger.error(f"❌ 字段类型保存错误: {field_config.get('field_type')}")
                return False
        else:
            logger.error("❌ 字段配置未找到")
            return False
        
        # 模拟配置恢复
        logger.info("🔄 模拟配置恢复")
        
        # 重新加载表格（模拟Sheet切换）
        widget.load_excel_headers(test_headers, "salary_table")
        
        # 应用保存的配置
        widget._apply_saved_mapping_config(saved_config)
        
        # 验证字段类型是否正确恢复
        field_type_combo_after = widget.mapping_table.cellWidget(0, 3)
        if field_type_combo_after:
            current_data = field_type_combo_after.currentData()
            if current_data == "employee_id":
                logger.info("✅ 字段类型恢复正确")
                return True
            else:
                logger.error(f"❌ 字段类型恢复错误: {current_data}")
                return False
        else:
            logger.error("❌ 字段类型下拉框未找到")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False


def test_config_format_compatibility():
    """测试配置格式兼容性"""
    logger.info("🧪 测试配置格式兼容性")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.unified_data_import_window import UnifiedMappingConfigWidget
        
        if not QApplication.instance():
            app = QApplication([])
        
        # 创建组件
        widget = UnifiedMappingConfigWidget()
        
        # 加载测试数据
        test_headers = ["姓名", "工号", "基本工资"]
        widget.load_excel_headers(test_headers, "salary_table")
        
        # 测试新格式配置
        new_format_config = {
            "姓名": {
                "target_field": "employee_name",
                "display_name": "员工姓名",
                "field_type": "employee_id",
                "data_type": "VARCHAR(50)",
                "is_required": True
            }
        }
        
        logger.info("📝 测试新格式配置应用")
        widget._apply_saved_mapping_config(new_format_config)
        
        # 验证应用结果
        field_type_combo = widget.mapping_table.cellWidget(0, 3)
        if field_type_combo and field_type_combo.currentData() == "employee_id":
            logger.info("✅ 新格式配置应用成功")
        else:
            logger.error("❌ 新格式配置应用失败")
            return False
        
        # 测试旧格式配置
        old_format_config = {
            "姓名": "employee_name",
            "工号": "employee_id",
            "基本工资": "base_salary"
        }
        
        logger.info("📝 测试旧格式配置应用")
        widget._apply_saved_field_mappings(old_format_config)
        
        # 验证应用结果
        db_field_item = widget.mapping_table.item(0, 1)
        if db_field_item and db_field_item.text() == "employee_name":
            logger.info("✅ 旧格式配置应用成功")
            return True
        else:
            logger.error("❌ 旧格式配置应用失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False


def run_field_type_persistence_tests():
    """运行字段类型持久化测试"""
    logger.info("🚀 开始字段类型持久化测试")
    
    results = []
    
    # 测试1: 字段类型保存和恢复
    logger.info("\n" + "="*50)
    logger.info("测试1: 字段类型保存和恢复")
    logger.info("="*50)
    result1 = test_field_type_save_and_restore()
    results.append(("字段类型保存和恢复", result1))
    
    # 测试2: 配置格式兼容性
    logger.info("\n" + "="*50)
    logger.info("测试2: 配置格式兼容性")
    logger.info("="*50)
    result2 = test_config_format_compatibility()
    results.append(("配置格式兼容性", result2))
    
    # 输出总结
    logger.info("\n" + "="*50)
    logger.info("🎯 字段类型持久化测试结果总结")
    logger.info("="*50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 所有字段类型持久化测试通过！")
        logger.info("💡 字段类型修改后的保存和恢复功能正常")
        return True
    else:
        logger.error("\n❌ 部分字段类型持久化测试失败")
        logger.error("⚠️ 需要进一步检查和修复")
        return False


if __name__ == "__main__":
    success = run_field_type_persistence_tests()
    sys.exit(0 if success else 1)
