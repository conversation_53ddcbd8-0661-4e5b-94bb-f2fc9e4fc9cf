#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
P0修复验证测试：字段映射数据丢失问题
测试字段类型修改后Sheet切换的数据保持性
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QComboBox, QTableWidgetItem
from PyQt5.QtCore import Qt
from src.gui.unified_data_import_window import UnifiedMappingConfigWidget
from src.utils.log_config import setup_logger

logger = setup_logger(__name__)


class TestP0FieldMappingFix(unittest.TestCase):
    """P0修复验证测试类"""

    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()

    def setUp(self):
        """每个测试前的设置"""
        self.widget = UnifiedMappingConfigWidget()
        
        # 模拟Excel字段头
        self.test_headers = ["姓名", "工号", "基本工资", "绩效奖金", "部门"]
        self.test_table_type = "salary_table"
        
        # 加载测试数据
        self.widget.load_excel_headers(self.test_headers, self.test_table_type)

    def test_table_item_changed_triggers_save(self):
        """测试表格项变化是否触发保存"""
        logger.info("🧪 测试：表格项变化触发保存")
        
        # 模拟config_sync_manager
        mock_config_sync = Mock()
        mock_config_sync.save_mapping.return_value = True
        self.widget.config_sync_manager = mock_config_sync
        
        # 获取第一行的某个单元格
        if self.widget.mapping_table.rowCount() > 0:
            # 修改显示名称列（第2列）
            item = QTableWidgetItem("修改后的姓名")
            self.widget.mapping_table.setItem(0, 2, item)
            
            # 手动触发itemChanged信号
            self.widget._on_table_item_changed(item)
            
            # 验证保存方法被调用
            self.assertTrue(mock_config_sync.save_mapping.called)
            logger.info("✅ 表格项变化成功触发保存")
        else:
            logger.warning("⚠️ 表格为空，跳过测试")

    def test_field_type_combo_change_triggers_save(self):
        """测试字段类型下拉框变化是否触发保存"""
        logger.info("🧪 测试：字段类型下拉框变化触发保存")
        
        # 模拟config_sync_manager
        mock_config_sync = Mock()
        mock_config_sync.save_mapping.return_value = True
        self.widget.config_sync_manager = mock_config_sync
        
        # 获取第一行的字段类型下拉框（第3列）
        if self.widget.mapping_table.rowCount() > 0:
            field_type_combo = self.widget.mapping_table.cellWidget(0, 3)
            if isinstance(field_type_combo, QComboBox):
                # 模拟下拉框变化
                original_sender = self.widget.sender
                self.widget.sender = lambda: field_type_combo
                
                # 触发字段类型变化
                self.widget._on_field_type_changed()
                
                # 恢复sender方法
                self.widget.sender = original_sender
                
                # 验证保存方法被调用
                self.assertTrue(mock_config_sync.save_mapping.called)
                logger.info("✅ 字段类型变化成功触发保存")
            else:
                logger.warning("⚠️ 字段类型下拉框未找到")
        else:
            logger.warning("⚠️ 表格为空，跳过测试")

    def test_save_current_config_method(self):
        """测试save_current_config方法"""
        logger.info("🧪 测试：save_current_config方法")
        
        # 模拟config_sync_manager
        mock_config_sync = Mock()
        mock_config_sync.save_mapping.return_value = True
        self.widget.config_sync_manager = mock_config_sync
        
        # 添加一些映射配置
        self.widget.mapping_config = {
            "姓名": {
                "target_field": "name",
                "display_name": "姓名",
                "field_type": "general",
                "data_type": "VARCHAR(100)",
                "is_required": True
            }
        }
        
        # 调用保存方法
        result = self.widget.save_current_config()
        
        # 验证结果
        self.assertTrue(result)
        self.assertTrue(mock_config_sync.save_mapping.called)
        logger.info("✅ save_current_config方法工作正常")

    def test_config_sync_manager_initialization(self):
        """测试ConfigSyncManager初始化"""
        logger.info("🧪 测试：ConfigSyncManager初始化")
        
        # 检查config_sync_manager是否已初始化
        self.assertIsNotNone(self.widget.config_sync_manager)
        logger.info("✅ ConfigSyncManager初始化成功")

    def test_mapping_config_update(self):
        """测试映射配置更新"""
        logger.info("🧪 测试：映射配置更新")
        
        # 确保表格有数据
        if self.widget.mapping_table.rowCount() > 0:
            # 修改表格数据
            self.widget.mapping_table.setItem(0, 1, QTableWidgetItem("modified_field"))
            self.widget.mapping_table.setItem(0, 2, QTableWidgetItem("修改后的显示名"))
            
            # 调用更新方法
            self.widget._update_mapping_config()
            
            # 检查映射配置是否更新
            self.assertGreater(len(self.widget.mapping_config), 0)
            logger.info(f"✅ 映射配置已更新，包含 {len(self.widget.mapping_config)} 个字段")
        else:
            logger.warning("⚠️ 表格为空，跳过测试")

    def tearDown(self):
        """每个测试后的清理"""
        if hasattr(self, 'widget'):
            self.widget.deleteLater()


def run_p0_fix_validation():
    """运行P0修复验证"""
    logger.info("🚀 开始P0修复验证测试")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestP0FieldMappingFix)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        logger.info("🎉 P0修复验证测试全部通过！")
        return True
    else:
        logger.error(f"❌ P0修复验证测试失败：{len(result.failures)} 个失败，{len(result.errors)} 个错误")
        return False


if __name__ == "__main__":
    success = run_p0_fix_validation()
    sys.exit(0 if success else 1)
